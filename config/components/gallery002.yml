default:
  options:
    name: "Instagram Feed Gallery"
    outer_container_layer:
      theme: "light"
      padding_y: "py-16 lg:py-24"
    inner_container_layer:
      theme: "light"
      padding_x: "px-4"
      container: "container"
    content_layer:
      theme: "light"
      padding_y: "sm:py-12"
      padding_x: "sm:px-12"
      container: "container-full"
      gap_y: "gap-y-6"
      alignment: "alignment-center"
  media_options:
    gap: 6
    posts_limit: 10
    type: gallery
  media_items:
    - image_url: "https://picsum.photos/seed/insta_a/400/400"
      title: "Ranní inspirace"
      published_at: "28. května 2025"
      caption: "Začněte den s námi! Denní dávka krásy a pozitivní energie přímo z našeho studia."
      url: "https://www.instagram.com/p/Cexample1/"
    - image_url: "https://picsum.photos/seed/insta_b/400/400"
      title: "Nové trendy"
      published_at: "27. května 2025"
      caption: "Sledujeme nejnovější trendy v beauty světě a přinášíme vám je jako první."
      url: "https://www.instagram.com/p/Cexample2/"
    - image_url: "https://picsum.photos/seed/insta_c/400/400"
      title: "Relaxace a péče"
      caption: "Dopřejte si chvilku pro sebe. Naše tipy na dokonalou relaxaci a péči."
      url: "https://www.instagram.com/p/Cexample3/"
    - image_url: "https://picsum.photos/seed/insta_d/400/400"
      title: "Ze zákulisí"
      published_at: "25. května 2025"
      caption: "Nahlédněte s námi do zákulisí příprav nových kolekcí a služeb."
      url: "https://www.instagram.com/p/Cexample4/"
    - image_url: "https://picsum.photos/seed/insta_e/400/400"
      title: "Váš názor nás zajímá"
      caption: "Co byste si přáli vidět více? Dejte nám vědět do komentářů!"
      url: "https://www.instagram.com/p/Cexample5/"
  controls:
    - type: "BlockControls::Heading"
      text: "Sledujte nás na Instagramu" # Komponenta automaticky zvýrazní "Instagramu"
      options:
        heading_type: "h2" # Může být h1, h2, h3...
        pre_header: "" # Tento blok ho nepoužívá
    - type: "BlockControls::Paragraph"
      text: "Denní dávka inspirace, zákulisí a nejnovějších trendů z našeho beauty prostoru"
    - type: "BlockControls::Button" # Komponenta použije tato data pro odkaz
      options:
        primary_button_text: "Sledovat @oficina_2.0"
        primary_button_link: "https://www.instagram.com/oficina_2.0/" # Doplňte skutečný odkaz
        primary_button_link_type: "link" # Naznačuje, že se má otevřít jako odkaz (target _blank)