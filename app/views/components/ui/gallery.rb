class Ui::Gallery < ApplicationComponent
  COLUMN_CLASSES = {
    1 => "grid-cols-1",
    2 => "grid-cols-2",
    3 => "grid-cols-2 sm:grid-cols-3",
    4 => "grid-cols-2 md:grid-cols-4",
    5 => "grid-cols-2 sm:grid-cols-3 lg:grid-cols-5",
    6 => "grid-cols-2 sm:grid-cols-3 lg:grid-cols-6",
    7 => "grid-cols-2 sm:grid-cols-4 lg:grid-cols-7",
    8 => "grid-cols-2 sm:grid-cols-4 lg:grid-cols-8",
    9 => "grid-cols-3 md:grid-cols-5 lg:grid-cols-9",
    10 => "grid-cols-3 md:grid-cols-5 lg:grid-cols-10"
    # Můžete pokračovat až do 12, což je běžné pro grid systémy.
  }.freeze

  def initialize(items, layout: :grid, columns: 3, gap: 3, resize_options: { resize_to_fill: [400, 400] }, item_classes: "")
    @items = items
    @layout = layout
    @columns = columns
    @gap = gap
    @resize_options = resize_options
    @item_classes = item_classes
  end

  def view_template
    case @layout.to_sym
    when :grid
      render_grid
    when :masonry
      render_masonry
    when :slider
      render_slider
    when :strip
      render_strip
    else
      render_grid
    end
  end

  private

  def columns_class
    COLUMN_CLASSES.fetch(@columns, "grid-cols-2 sm:grid-cols-3")
  end

  def render_grid
    div(class: "grid grid-cols-2 #{columns_class} gap-#{@gap}") do
      @items.each do |item|
        div(class: @item_classes) do
          Ui::Image(item, resize_options: @resize_options, classes: "w-full h-full object-cover")
        end
      end
    end
  end

  def render_masonry
    div(class: "columns-1 sm:columns-2 md:columns-#{@columns} gap-#{@gap} space-y-#{@gap}") do
      @items.each do |item|
        div(class: "break-inside-avoid #{@item_classes}") do
          Ui::Image(item, resize_options: @resize_options, classes: "w-full h-auto object-cover")
        end
      end
    end
  end

  def render_slider
    div(data: { controller: "splide" }, class: "relative splide") do
      div(class: "splide__track") do
        ul(class: "splide__list") do
          @items.each do |item|
            li(class: "splide__slide") do
              div(class: @item_classes) do
                Ui::Image(item, resize_options: @resize_options, classes: "w-full h-full object-cover")
              end
            end
          end
        end
      end
    end
  end

  def render_strip
    div(class: "grid grid-cols-3 md:grid-cols-#{@columns} gap-#{@gap}") do
      @items.each do |item|
        div(class: "even:mt-4 even:lg:mt-10") do
          div(class: "relative rounded-box") { Ui::Image(item, resize_options: @resize_options, classes: "w-full rounded-box h-full object-cover") }
        end
      end
    end
  end
end
