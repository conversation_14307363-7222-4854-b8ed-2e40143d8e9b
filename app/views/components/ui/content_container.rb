# frozen_string_literal: true

class Ui::ContentContainer < Phlex::HTML
  attr_reader :controls, :block_id, :content_layer, :css, :style

  def initialize(controls, block_id, content_layer, css: nil, style: nil)
    @controls = controls
    @block_id =  block_id
    @content_layer = content_layer
    @css = css
    @style = style
  end

  def view_template
    div(
      id: dom_id,
      data: theme_data,
      class: "#{parse_css} #{@content_layer.padding_y} #{@content_layer.padding_x } #{@content_layer.alignment} #{@content_layer.gap_y} #{static_css}"
    ) do
      controls.each do |control|
        render control.component
      end
    end
  end

  private

  def theme_data
    { theme: @content_layer.theme }
  end

  def static_css
    "mx-auto px-4 m-4 relative z-10 flex flex-col"
  end

  def dom_id
    "block-#{@block_id}-content-layer"
  end
end
