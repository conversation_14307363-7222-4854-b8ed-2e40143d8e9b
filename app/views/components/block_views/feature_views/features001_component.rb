module BlockViews
  class FeatureViews::Features001Component < BaseComponent
    def view_template
      container do
        inner_container do
          content_container(block_presenter.controls)

            media_container do
              render ::Ui::Features(
                features,
                layout: block_presenter.media.options[:layout] || :default,
                columns: 3,
                gap: block_presenter.media.options[:gap] || 3
              )
            end
        end
      end
    end

    private

    def features
      block_presenter.media.items
    end
  end
end
