module BlockViews
  class FeatureViews::Features002Component < BaseComponent
    def view_template
      div(class: "py-16", data_theme: "soft") do
        div(class: "max-w-4xl mx-auto") do
          h1(class: "h1 text-center text-5xl") { "Oficína - prostor pro tělo a klid" }
          p(class: "text-center mt-5") do
            "Jedno místo kde se postaráš o celé své já. Oficína neni slu<PERSON>. Je to prožitek."
          end
          div(class: "flex justify-center space-x-3 mt-5 items-center") do
            a(
              class: "btn btn-accent",
              target: "_blank",
              href:
                "https://oficina.snippet.myfox.cz/form/show/date_2025-06-20/shopId_cksbw9b4fmasr0883nvbnppe7/lng_cs"
            ) { "Objednat se" }
            a(class: "link link-hover text-base-content", href: "tel:123456789") do
              "Kontakt"
            end
          end
          div(class: "bg-accent/20 my-8 h-px")
          div(class: "grid grid-cols-4 grid-cols-2") do
            div(class: "flex flex-col items-center justify-center") do
              span(class: "text-3xl font-medium") { "5+" }
              span { "Let zkušeností" }
            end
            div(class: "flex flex-col items-center justify-center") do
              span(class: "text-3xl font-medium") { "100+" }
              span { "Spokojených zákazníků" }
            end
            div(class: "flex flex-col items-center justify-center") do
              span(class: "text-3xl font-medium") { "4" }
              span { "Členové týmu" }
            end
            div(class: "flex flex-col items-center justify-center") do
              span(class: "text-3xl font-medium") { "100%" }
              span { "Spokojenost" }
            end
          end
        end
      end
    end
  end
end
