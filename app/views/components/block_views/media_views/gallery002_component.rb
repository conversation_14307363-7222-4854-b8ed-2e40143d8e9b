module BlockViews
  class MediaViews::Gallery002Component < BaseComponent
    # --- <PERSON><PERSON> IKONY (natvrdo v komponentě pro gallery002) ---
    HEADER_ICON_SVG = '<svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>'.freeze
    BUTTON_ICON_SVG = '<svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>'.freeze
    MEDIA_ITEM_INSTAGRAM_ICON_SVG = '<svg class="w-5 h-5 text-pink-500" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>'.freeze # Doplňte celé SVG, pokud je jiné
    MEDIA_ITEM_LINK_ARROW_SVG = '<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path></svg>'.freeze
    # ----------------------------------------------------

    def view_template
      # css: "bg-gradient-to-br from-base-100 to-base-200"
      container do
        inner_container do
          div(class: "flex justify-center mb-6") do
            div(class: "w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg") do
              HEADER_ICON_SVG.html_safe
            end
          end

          content_container(@block_presenter.controls)

          media_container do
            render Ui::Gallery.new(
              block_presenter.media.items,
              layout: block_presenter.media.options[:layout] || :grid,
              columns: 6,
              gap: block_presenter.media.options[:gap] || 3,
              resize_options: resize_image_options,
              item_classes: "relative aspect-square overflow-hidden rounded-md"
            )
          end
        end
      end
    end

    private

    def resize_image_options
      { resize_to_fill: [200, 200] }
    end

    def render_header_content
      heading_ctrl = block_presenter.controls.find { |c| c.type == "BlockControls::Heading" }
      paragraph_ctrl = block_presenter.controls.find { |c| c.type == "BlockControls::Paragraph" }
      button_ctrl = block_presenter.controls.find { |c| c.type == "BlockControls::Button" }

      text_align_class = block_presenter.content_layer&.alignment == "left" ? "text-left" : "text-center"
      header_margin_bottom = "mb-12" # Pevně z HTML
      paragraph_max_width = block_presenter.content_layer&.container || "max-w-2xl" # Pro <p>

      # Použijeme `content_container` pokud je to jen obecný wrapper s paddingy z content_layer,
      # a dovnitř dáme naši strukturu. Pokud `content_container` dělá víc (např. iteruje controls),
      # tak ho zde nepoužijeme a třídy aplikujeme na `div`.
      # V `Hero005` `content_container` iteruje controls. Takže ho zde nepoužijeme pro celou hlavičku.
      # Místo toho použijeme obyčejný `div` a aplikujeme relevantní styly.

      div(class: "#{text_align_class} #{header_margin_bottom}") do
        # Ikona
        div(class: "flex justify-center mb-6") do
          div(class: "w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg") do
            HEADER_ICON_SVG.html_safe
          end
        end

        # Nadpis
        if heading_ctrl
          # `render heading_ctrl.component` by použilo standardní komponentu nadpisu.
          # Pro gradientní text potřebujeme vlastní renderování.
          custom_render_heading(heading_ctrl)
        end

        # Odstavec
        if paragraph_ctrl
          # Použijeme standardní komponentu odstavce, pokud umí pracovat s `content_layer.container` pro šířku.
          # Nebo renderujeme <p> přímo s třídami.
          # `render paragraph_ctrl.component`
          p(class: "text-xl text-base-content/70 #{paragraph_max_width} mx-auto mb-8") { plain paragraph_ctrl.text }
        end

        # Tlačítko (interpretované jako odkaz)
        if button_ctrl && button_ctrl.options[:primary_button_text].present?
          # `render button_ctrl.component` by použilo standardní tlačítkovou komponentu.
          # Pro specifický styl odkazu `gallery002` renderujeme přímo.
          custom_render_button_link(button_ctrl)
        end
      end
    end
  end
end