<% if medium.persisted? %>
  <% url = admin_content_medium_path(medium, block_id: block.id) %>
  <% method = :patch %>
<% else %>
  <% url = admin_content_media_path(block_id: block.id) %>
  <% method = :post %>
<% end %>

<%= form_with model: medium, url: url, method: method, id: "media-block-form", class: "flex flex-col h-full" do |f| %>
  <div class="flex mt-4 flex-col space-y-4">
    <%= f.hidden_field :block_id, value: block.id %>
    <%= f.hidden_field :media_type_id, value: medium.media_type_id %>

    <% if medium.media_type %>
      <%# Vnořený formulář pro přeložitelný obsah %>
      <%= f.fields_for :contents, medium.content_for(I18n.locale) do |content_fields| %>
        <%= content_fields.hidden_field :locale, value: I18n.locale %>

        <% medium.media_type.media_fields.order(:position).each do |field| %>
          <div class="form-group">
            <label class="text-sm"><%= field.field_key.humanize %><%= field.required ? '<span class="required">*</span>'.html_safe : '' %></label>
            <div>
              <% case field.field_key %>
              <% when "title" %>
                <%= content_fields.text_field :title, class: "input" %>
              <% when "text" %>
                <%= content_fields.text_area :text, class: "textarea" %>
              <% when "content" %>
                <div class="w-full h-auto">
                  <div data-controller="tiptap" data-tiptap-preview-element-id-value="<%= "media-#{f.object.id}-text" %>" class="text-base" data-tiptap-text-value="<%= content_fields.object.text %>">
                    <%= content_fields.hidden_field :text, placeholder: "Text nadpisu", data: { 'tiptap-target': 'inputText' } %>
                    <%= content_fields.hidden_field :content, class: "input", value: content_fields.object.content.to_json, data: { 'tiptap-target': 'inputContent' } %>

                    <div data-tiptap-target="content" data-theme="<%= block.theme %>" class="text-black bg-white rounded-none rounded-r h-auto rounded border border-gray-300 p-2"></div>

                    <%= render "/admin/content/page_blocks/controls/tiptap_toolbar" %>
                  </div>
                </div>
              <% when "caption" %>
                <%= content_fields.text_field :caption, class: "input" %>
              <% else %>
                <%# Dynamická pole z custom_fields %>
                <% case field.field_type %>
                <% when 'string' %>
                  <%= content_fields.fields_for :custom_fields do |custom_f| %>
                    <%= custom_f.text_field field.field_key, value: content_fields.object.custom_field(field.field_key), class: "input" %>
                  <% end %>
                <% when 'text' %>
                  <%= content_fields.fields_for :custom_fields do |custom_f| %>
                    <%= custom_f.text_area field.field_key, value: content_fields.object.custom_field(field.field_key), class: "textarea" %>
                  <% end %>
                <% when 'image' %>
                  <div class="flex space-x-2 items-center mb-1">
                    <% if medium.image.attached? %>
                      <%= image_tag medium.image.variant(:thumb), class: "w-6 h-6 rounded-full" %>
                    <% end %>
                    <%= f.file_field :image, class: "file-input" %>
                  </div>
                  <% if medium.errors.messages_for(:image).any? %>
                    <%= error_message "Obrázek je povinný" %>
                  <% end %>
                <% when 'icon' %>
                  <div data-controller="icon-picker">
                    <div class="flex items-center space-x-2">
                      <button type="button" data-action="click->icon-picker#toggle" class="btn btn-sm">
                      <span data-icon-picker-target="preview">
                        <%= medium.icon ? medium.icon.with(size: 4) : "Vybrat ikonu" %>
                      </span>
                      </button>
                    </div>
                    <%= f.hidden_field :icon_id, data: { 'icon-picker-target': 'hiddenInput'} %>
                    <div data-icon-picker-target="dropdown" class="dropdown-content max-h-[300px] overflow-scroll grid-cols-10">
                      <% Icon.all.each do |icon| %>
                        <% encoded_icon = Base64.strict_encode64(icon.with(size: 4).to_s) %>
                        <button type="button" class="p-1 hover:bg-gray-200 cursor-pointer rounded"
                                data-action="click->icon-picker#select"
                                data-icon-picker-icon="<%= icon.id %>"
                                data-icon-picker-icon-html="<%= encoded_icon %>">
                          <%= icon.with(size: 4) %>
                        </button>
                      <% end %>
                    </div>
                  </div>
                  <% if medium.errors.messages_for(:icon_id).any? %>
                    <%= error_message "Ikona je povinná" %>
                  <% end %>
                <% when 'rating' %>
                  <%= content_fields.fields_for :custom_fields do |custom_f| %>
                    <div class="flex space-x-1">
                      <% (1..5).each do |i| %>
                        <label class="cursor-pointer">
                          <%= custom_f.radio_button field.field_key, i, checked: content_fields.object.custom_field(field.field_key).to_i == i %>
                          <span class="<%= i <= content_fields.object.custom_field(field.field_key).to_i ? 'text-yellow-400' : 'text-gray-300' %>">★</span>
                        </label>
                      <% end %>
                    </div>
                  <% end %>
                <% end %>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    <% else %>
      <div class="text-gray-500">Pro tento typ média nejsou definována žádná pole.</div>
    <% end %>

    <div class="flex mt-1">
      <a href="<%= edit_admin_content_page_block_path(@owner, @block) %>" data-turbo-method="get" class="bg-gray-200 rounded-l hover:bg-gray-300 py-2 px-4 text-center text-white cursor-pointer">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-gray-900">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
        </svg>
      </a>

      <%= f.submit "Uložit", class: "bg-gray-800 hover:bg-black p-2 rounded-r text-center text-white flex-1 cursor-pointer" %>
    </div>
  </div>
<% end %>
