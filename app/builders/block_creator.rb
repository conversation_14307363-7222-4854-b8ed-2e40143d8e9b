# app/services/block_from_template_factory.rb
require 'open-uri'

class BlockCreator
  def self.build(block_data:, website: Current.website)
    new(block_data: block_data, website: website).build
  end

  def self.create(block_data:, website: Current.website)
    new(block_data: block_data, website: website).create
  end

  def initialize(block_data:, website:)
    @block_data = block_data
    @website = website

    @block = Block.new
  end

  def build
    build_main_block_attributes
    build_controls
    build_media_collection_and_items if @block_data.media_data.present?
    @block
  end

  def create
    Block.transaction do
      build.save!
    end
    @block
  end

  private

  def build_main_block_attributes
    @block.assign_attributes(
      name: @block_data.name,
      options: @block_data.options,
      media_options: @block_data.media_data[:options]
    )
  end

  def build_controls
    return if @block_data.controls_data.blank?

    available_locales.each do |locale|
      @block_data.controls_data.each do |control_data|
        @block.controls.build(
          type: control_data[:type],
          options: (control_data[:options] || {}).deep_symbolize_keys,
          text: control_data[:text],
          position: control_data[:position],
          locale: locale
        )
      end
    end
  end

  def build_media_collection_and_items
    return unless media_type

    @block.build_media_collection(
      name: "Kolekce pro blok '#{@block.name}'",
      collection_type: "GalleryMediaCollection",
      media_type: media_type
    )

    @block.media_type = media_type

    preload_icons

    @block_data.media_data[:items_payload].each do |item_template|
      build_single_media_item(item_template)
    end
  end

  def build_single_media_item(item_template)
    media_item = @block.media_collection.media.build(
      website: @website,
      media_type: media_type,
      icon: find_preloaded_icon(item_template),
    )

    media_item.contents.build(
      title: safe_read(item_template, :title, default: "Nepojmenováno"),
      content: safe_read(item_template, :content),
      custom_fields: (safe_read(item_template, :data) || {}).deep_symbolize_keys,
      locale: :cs
    )

    if item_template[:image_url].present?
      media_item.image.attach(
        io: URI.open(item_template[:image_url]),
        filename: "#{File.basename(URI.parse(item_template[:image_url]).path)}.jpg"
      )
    end
  end

  def attach_image_to(media_item, from:)
    image_io, filename = find_image_source(from)
    return unless image_io && filename

    media_item.image.attach(io: image_io, filename: filename)
  rescue OpenURI::HTTPError, StandardError => e
    # Přidáme chybu k modelu, aby `.save` selhalo a transakce se vrátila zpět.
    @block.errors.add(:base, "Nepodařilo se stáhnout obrázek z #{from.image_url}: #{e.message}")
    Rails.logger.error "Chyba při stahování obrázku pro blok #{@block_data.name}: #{e.message}"
  end

  def media_type
    # Memoizace, abychom nehledali v DB opakovaně
    @media_type ||= MediaType.find_by(slug: @block_data.media_data[:type])
  end

  def preload_icons
    icon_names = @block_data.media_data[:items_payload].map do |item|
      next unless item.respond_to?(:icon) && item.icon.present?
      item.icon.is_a?(String) ? item.icon : item.icon.try(:name)
    end.compact.uniq

    @preloaded_icons = Icon.where(name: icon_names).index_by(&:name)
  end

  def find_preloaded_icon(media_item_obj)
    return nil unless media_item_obj.respond_to?(:icon) && media_item_obj.icon.present?
    icon_identifier = media_item_obj.icon.is_a?(String) ? media_item_obj.icon : media_item_obj.icon.try(:name)
    @preloaded_icons[icon_identifier]
  end

  def available_locales
    @website&.available_locales.presence || ['cs']
  end

  def safe_read(object, method, default: nil)
    object.respond_to?(method) ? object.public_send(method) : default
  end
end