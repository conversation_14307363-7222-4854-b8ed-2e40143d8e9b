# app/services/block_config_adapter.rb
class BlockConfigAdapter < ConfigAdapter
  def initialize(block_record) # Přejmenováno z 'block' na 'block_record' pro jasnost
    @block = block_record
  end

  def id
    @block.id
  end

  def name
    @block.name
  end

  # Metoda options nyní vrací celý hash z block.options,
  # který již obsahuje *_layer_attributes pro všechny vrstvy.
  def options
    # Zajistíme deep_symbolize_keys pro konzistenci
    (@block.options || {}).deep_symbolize_keys
  end

  def controls_data(locale)
    @block.controls_for_locale(locale).map do |control|
      {
        type: control.type,
        id: control.id,
        text: control.text,
        options: (control.options || {}).deep_symbolize_keys, # <PERSON>ajist<PERSON>me symbolizaci i zde
        position: control.position,
        locale: control.locale
      }
    end
  end

  def media_data
    items_array = (@block.media).map do |media_item_ar_object|
      {
        id: media_item_ar_object.id,
        title: media_item_ar_object.title,
        image_attachment: media_item_ar_object.image.attached? ? media_item_ar_object.image : nil
      }
    end

    {
      type: @block.media_type&.slug,
      layout: @block.media_layout,
      options: @block.media_options,
      items_payload: items_array,
    }.deep_symbolize_keys
  end

  def pricing_id
    @block.pricing_id
  end

  def pricing_options
    (@block.pricing_options || {}).deep_symbolize_keys
  end

  def background_image_attachment
    @block.background_image if @block.background_image.attached? # Kontrola attached? je dobrá
  end

  def background_image_mobile_attachment
    @block.background_image_mobile if @block.background_image_mobile.attached?
  end
end