class Apify::Client
  attr_accessor :token

  API_BASE_URL = "https://api.apify.com/v2/".freeze

  GOOGLE_REVIEWS_START_URL = "https://api.apify.com/v2/acts/compass~crawler-google-places/runs?token=**********************************************"
  INSTAGRAM_START_URL = "https://api.apify.com/v2/actor-tasks/detailed_spray~instagramwinweb/runs?token=**********************************************"

  def initialize(token)
    @token = token
    @connection = Faraday.new(url: API_BASE_URL) do |conn|
      conn.request :json
      conn.response :json, content_type: /\bjson$/
      conn.adapter Faraday.default_adapter
    end
  end

  # client = Apify::Client.new('**********************************************')
  # client.fetch_google_reviews(places: ['https://www.google.com/maps/place/Jihočeská+Salaš/@48.967532,14.3206473,17z/data=!3m1!4b1!4m9!3m8!1s0x4773575a6a7481e9:0xdb004da98813742c!5m2!4m1!1i2!8m2!3d48.967532!4d14.3232222!16s%2Fg%2F11b6gdd_qr?entry=ttu'])
  def fetch_google_reviews(places: [])
    response = @connection.post(GOOGLE_REVIEWS_START_URL) do |request|
      request.body = {
        startUrls: places.map { |url| { url: url } }
      }
    end
    handle_response(response)
  end

  def fetch_instagram_posts(options: {}, params: [])
    response = @connection.post(INSTAGRAM_START_URL) do |request|
      request.body = {
        directUrls: params.map { |param| param[:url] },
        userData: { data: params },
        resultsLimit: options[:resultsLimit]
      }

      request.body[:onlyPostsNewerThan] = options[:onlyPostsNewerThan] if options[:onlyPostsNewerThan].present?
    end

    handle_response(response)
  end

  def start_actor(actor_id, body = {})
    response = @connection.post("actor-tasks/#{actor_id}/runs") do |request|
      request.params["token"] = @token
      request.body = body
    end
    handle_response(response)
  end

  def get_dataset_items(dataset_id)
    response = @connection.get("datasets/#{dataset_id}/items") do |request|
      request.params["token"] = @token
    end
    handle_response(response)
  end

  def get_key_value_store_items(key_value_store_id)
    response = @connection.get("key-value-stores/#{key_value_store_id}/records/INPUT") do |request|
      request.params["token"] = @token
    end
    handle_response(response)
  end

  private

  def handle_response(response)
    if response.success?
      response.body
    else
      raise "Error: #{response.status} - #{response.body}"
    end
  end
end
